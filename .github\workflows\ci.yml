name: SIGMA CI - Build, Lint & Test
on:
  pull_request:
    branches: [ main ]
  workflow_dispatch:

jobs:
  build-and-test:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        
      - name: Setup Node.js v20
        uses: actions/setup-node@v4
        with:
          node-version: 20
          cache: 'npm'
          cache-dependency-path: 'functions/package-lock.json' 
          
      - name: Install Dependencies
        working-directory: ./functions 
        run: npm ci
        
      - name: Validate ESLint configuration
        working-directory: ./functions 
        run: npx eslint --print-config src | head -20
        
      - name: Format code with Prettier
        working-directory: ./functions 
        run: npx prettier --write src/**/*.ts
        
      - name: Run Linter
        working-directory: ./functions 
        run: npm run lint -- --fix
        
      - name: Run Unit Tests
        working-directory: ./functions 
        run: npm run test:ci
        
      - name: Build TypeScript
        working-directory: ./functions 
        run: npm run build